import { NextRequest, NextResponse } from 'next/server';
import { updateDocumentStatus, getDocumentById, runQuery } from '@/lib/database';
import { processDocumentForApproval } from '@/lib/qr-document-utils';

/**
 * PUT /api/documents/[id]/status - Update document status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status, approved_at } = body;

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Get document from database first to verify it exists
    const document = await getDocumentById(documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // If approving the document, add QR code to document data
    if (status === 'approved' && document.document_data && document.code) {
      try {
        // Parse existing document data
        const documentDataString = document.document_data.toString();
        let documentData: any = {};

        try {
          documentData = JSON.parse(documentDataString);
        } catch (parseError) {
          console.warn('Could not parse document data as JSON, treating as plain text');
          documentData = { content: documentDataString };
        }

        // Add QR code to document data
        const updatedDocumentData = await processDocumentForApproval(documentData, document.code);

        // Update document with new data including QR code
        await runQuery(
          'UPDATE documents SET status = ?, approved_at = ?, document_data = ? WHERE id = ?',
          [status, approved_at, Buffer.from(JSON.stringify(updatedDocumentData)), documentId]
        );

        console.log(`QR code added to document ${documentId} with code ${document.code}`);
      } catch (qrError) {
        console.error('Error adding QR code to document:', qrError);
        // Continue with normal status update if QR code generation fails
        await updateDocumentStatus(documentId, status, approved_at);
      }
    } else {
      // Normal status update for non-approval cases or missing data
      await updateDocumentStatus(documentId, status, approved_at);
    }

    // Get updated document
    const updatedDocument = await getDocumentById(documentId);

    return NextResponse.json(
      { 
        message: 'Document status updated successfully',
        document: updatedDocument
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error updating document status:', error);
    return NextResponse.json(
      { error: 'Failed to update document status' },
      { status: 500 }
    );
  }
}
