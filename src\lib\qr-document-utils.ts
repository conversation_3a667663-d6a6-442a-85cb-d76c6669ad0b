/**
 * Utility functions for adding QR codes to documents when approved
 */

import QRCode from 'qrcode';
import { jsPDF } from 'jspdf';

export interface DocumentData {
  [key: string]: string;
}

export interface QRCodeOptions {
  size: number;
  margin: number;
  color: {
    dark: string;
    light: string;
  };
}

/**
 * Generate QR code data URL for a document code
 */
export async function generateQRCodeDataURL(
  documentCode: string,
  options: QRCodeOptions = {
    size: 100,
    margin: 2,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  }
): Promise<string> {
  try {
    return await QRCode.toDataURL(documentCode, {
      width: options.size,
      margin: options.margin,
      color: options.color,
      errorCorrectionLevel: 'M'
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Add QR code to document data by modifying the document_data JSON
 * This adds a qr_code field that can be used in templates
 */
export function addQRCodeToDocumentData(
  documentData: DocumentData,
  qrCodeDataURL: string
): DocumentData {
  return {
    ...documentData,
    qr_code: qrCodeDataURL
  };
}

/**
 * Create a new PDF with QR code overlay at bottom left
 * This is used when we need to add QR code to an existing document
 */
export async function addQRCodeToPDF(
  originalPDFBuffer: Buffer,
  documentCode: string,
  options: {
    position?: { x: number; y: number };
    size?: number;
  } = {}
): Promise<Buffer> {
  try {
    // Generate QR code
    const qrCodeDataURL = await generateQRCodeDataURL(documentCode, {
      size: options.size || 80,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // Create new PDF with QR code
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Convert original PDF to image and add to new PDF
    // Note: This is a simplified approach. In a real implementation,
    // you might want to use pdf-lib for better PDF manipulation
    
    // For now, we'll create a simple overlay approach
    // Add the QR code at bottom left (default position)
    const position = options.position || { x: 10, y: 280 }; // Bottom left in mm
    const size = (options.size || 80) * 0.264583; // Convert pixels to mm (96 DPI)

    pdf.addImage(
      qrCodeDataURL,
      'PNG',
      position.x,
      position.y,
      size,
      size
    );

    // Return the PDF as buffer
    const pdfArrayBuffer = pdf.output('arraybuffer');
    return Buffer.from(pdfArrayBuffer);
  } catch (error) {
    console.error('Error adding QR code to PDF:', error);
    throw new Error('Failed to add QR code to PDF');
  }
}

/**
 * Check if document data already has a QR code
 */
export function hasQRCode(documentData: DocumentData): boolean {
  return 'qr_code' in documentData && !!documentData.qr_code;
}

/**
 * Extract document code from QR code data (if needed for validation)
 */
export function extractCodeFromQRData(qrCodeData: string): string | null {
  try {
    // If the QR code data is just the document code, return it
    // This is a simple implementation - you might need more complex parsing
    if (qrCodeData.startsWith('DOC-')) {
      return qrCodeData;
    }
    return null;
  } catch (error) {
    console.error('Error extracting code from QR data:', error);
    return null;
  }
}

/**
 * Validate document code format
 */
export function isValidDocumentCode(code: string): boolean {
  // Check if code matches the format DOC-YYYYMMDD-XXXX
  const codePattern = /^DOC-\d{8}-\d{4}$/;
  return codePattern.test(code);
}

/**
 * Generate QR code HTML element for templates
 * This creates an HTML img element that can be inserted into document templates
 */
export function generateQRCodeHTML(
  qrCodeDataURL: string,
  options: {
    alt?: string;
    className?: string;
    style?: string;
  } = {}
): string {
  const alt = options.alt || 'Document QR Code';
  const className = options.className || 'qr-code';
  const style = options.style || 'width: 80px; height: 80px; position: absolute; bottom: 10px; left: 10px;';

  return `<img src="${qrCodeDataURL}" alt="${alt}" class="${className}" style="${style}" />`;
}

/**
 * Process document for approval - adds QR code to document data
 */
export async function processDocumentForApproval(
  documentData: DocumentData,
  documentCode: string
): Promise<DocumentData> {
  try {
    // Check if QR code already exists
    if (hasQRCode(documentData)) {
      console.log('Document already has QR code, skipping generation');
      return documentData;
    }

    // Validate document code
    if (!isValidDocumentCode(documentCode)) {
      throw new Error(`Invalid document code format: ${documentCode}`);
    }

    // Generate QR code
    const qrCodeDataURL = await generateQRCodeDataURL(documentCode);

    // Add QR code to document data
    const updatedDocumentData = addQRCodeToDocumentData(documentData, qrCodeDataURL);

    console.log(`QR code added to document data for code: ${documentCode}`);
    return updatedDocumentData;
  } catch (error) {
    console.error('Error processing document for approval:', error);
    throw error;
  }
}
